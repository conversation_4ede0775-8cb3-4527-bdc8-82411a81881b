<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Moverzz - Rental Mobile App</title>
    <style>
        body {
            font-family: 'Verdana', 'Geneva', sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);
            color: #2d3748;
            min-height: 100vh;
            line-height: 1.6;
        }
        .container {
            max-width: 900px;
            margin: 0 auto;
            background: #ffffff;
            border-radius: 20px;
            box-shadow: 0 16px 48px rgba(0, 0, 0, 0.12);
            overflow: hidden;
            margin-top: 20px;
            margin-bottom: 20px;
            position: relative;
        }
        .container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 6px;
            background: linear-gradient(90deg, #ff9a9e 0%, #fad0c4 50%, #ffd1ff 100%);
        }
        .header {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            color: white;
            padding: 60px 30px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }
        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><polygon points="0,0 100,0 80,100 0,100" fill="rgba(255,255,255,0.05)"/></svg>');
            background-size: 200px 100%;
            animation: slide 15s infinite linear;
        }
        @keyframes slide {
            0% { transform: translateX(-200px); }
            100% { transform: translateX(100vw); }
        }
        h1 {
            font-size: 3em;
            margin: 0 0 15px 0;
            font-weight: 400;
            letter-spacing: -1px;
            position: relative;
            z-index: 1;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        .tagline {
            font-size: 1.3em;
            opacity: 0.95;
            font-weight: 300;
            position: relative;
            z-index: 1;
            max-width: 650px;
            margin: 0 auto;
        }
        .content {
            padding: 40px 30px;
        }
        .project-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }
        .info-card {
            background: linear-gradient(145deg, #fef5e7 0%, #fed7aa 20%, #fef5e7 100%);
            padding: 35px;
            border-radius: 16px;
            border: 2px solid #fed7aa;
            box-shadow: 0 6px 20px rgba(254, 215, 170, 0.2);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        .info-card::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.3) 0%, transparent 70%);
            transform: scale(0);
            transition: transform 0.3s ease;
        }
        .info-card:hover::before {
            transform: scale(1);
        }
        .info-card:hover {
            transform: translateY(-6px) scale(1.02);
            box-shadow: 0 12px 32px rgba(254, 215, 170, 0.3);
        }
        .tech-stack {
            margin-top: 10px;
        }
        .tech-tag {
            background: #DBEAFE;
            padding: 3px 8px;
            border: 1px solid #3B82F6;
            font-size: 0.8em;
            margin: 2px;
            display: inline-block;
        }
        .features {
            list-style: none;
            padding: 0;
            margin: 10px 0;
        }
        .features li {
            padding: 5px 0;
            border-bottom: 1px solid #C0C0C0;
        }
        .features li:before {
            content: "• ";
            color: #000080;
            font-weight: bold;
        }
        .back-btn {
            background: #ECE9D8;
            color: black;
            border: 2px outset #ECE9D8;
            padding: 5px 15px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin-top: 15px;
            font-family: Tahoma, Arial, sans-serif;
        }
        .back-btn:hover {
            background: #D4D0C8;
        }
        h3 {
            color: #000080;
            font-size: 1.1em;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- <h1>Moverzz Mobile Application</h1> -->
        <p style="text-align: center; font-size: 1.2em; margin-bottom: 30px;">
            This app is under development.
            <!-- The ultimate rental mobile app connecting people who need to move with trusted movers and equipment. -->
        </p>
        
        <div class="project-info">
            <div class="info-card">
                <h3>🛠️ Technology Stack</h3>
                <div class="tech-stack">
                    <span class="tech-tag">React Native</span>
                    <span class="tech-tag">Node.js</span>
                    <span class="tech-tag">MongoDB</span>
                    <span class="tech-tag">Express.js</span>
                </div>
            </div>
            
            <div class="info-card">
                <h3>📊 Project Status</h3>
                <p><strong>Status:</strong> In progress</p>
                <!-- <p><strong>Version:</strong> 3.2.1</p>
                <p><strong>Downloads:</strong> 50K+ downloads</p>
                <p><strong>Rating:</strong> 4.7/5 stars</p> -->
            </div>
        </div>
        
        <!-- <div class="info-card">
            <h3>✨ Key Features</h3>
            <ul class="features">
                <li>Real-time mover tracking and GPS navigation</li>
                <li>Instant quotes and transparent pricing</li>
                <li>In-app messaging and video calls</li>
                <li>Secure payment processing and escrow</li>
                <li>Equipment rental marketplace</li>
                <li>Review and rating system</li>
                <li>Insurance coverage options</li>
                <li>Photo documentation and inventory tracking</li>
            </ul>
        </div>
        
        <div class="info-card">
            <h3>📝 Description</h3>
            <p>
                Moverzz revolutionizes the moving industry by connecting customers with verified, professional 
                movers through an intuitive mobile platform. Whether you're moving across town or across the 
                country, Moverzz makes the process seamless and stress-free.
            </p>
            <p>
                The app features real-time tracking, transparent pricing, secure payments, and comprehensive 
                insurance options. Users can browse mover profiles, read reviews, get instant quotes, and 
                manage their entire move from their smartphone. Available on both iOS and Android platforms.
            </p>
        </div> -->
    </div>
</body>
</html>
