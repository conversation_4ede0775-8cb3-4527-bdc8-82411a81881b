<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chromepanion - Browser Extension</title>
    <style>
        body {
            font-family: 'Segoe UI', 'Trebuchet MS', Verdana, sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            color: #2d3748;
            min-height: 100vh;
            line-height: 1.6;
        }
        .container {
            max-width: 900px;
            margin: 0 auto;
            background: #ffffff;
            border-radius: 16px;
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
            overflow: hidden;
            margin-top: 20px;
            margin-bottom: 20px;
            position: relative;
        }
        .container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
        }
        .header {
            background: linear-gradient(135deg, #2d3748 0%, #4a5568 100%);
            color: white;
            padding: 50px 30px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }
        .header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 1px, transparent 1px);
            background-size: 30px 30px;
            animation: float 20s infinite linear;
        }
        @keyframes float {
            0% { transform: translate(-50%, -50%) rotate(0deg); }
            100% { transform: translate(-50%, -50%) rotate(360deg); }
        }
        h1 {
            font-size: 2.8em;
            margin: 0 0 15px 0;
            font-weight: 300;
            letter-spacing: -2px;
            position: relative;
            z-index: 1;
        }
        .tagline {
            font-size: 1.2em;
            opacity: 0.9;
            font-weight: 300;
            position: relative;
            z-index: 1;
            max-width: 600px;
            margin: 0 auto;
        }
        .content {
            padding: 40px 30px;
        }
        .project-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
            margin-bottom: 40px;
        }
        .info-card {
            background: linear-gradient(145deg, #f7fafc 0%, #edf2f7 100%);
            padding: 30px;
            border-radius: 12px;
            border: 1px solid #e2e8f0;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
        }
        .info-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
        }
        .tech-stack {
            margin-top: 20px;
        }
        .tech-tag {
            display: inline-block;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 8px 16px;
            margin: 4px;
            font-size: 0.9em;
            border-radius: 20px;
            font-weight: 500;
            box-shadow: 0 3px 8px rgba(102, 126, 234, 0.3);
            border: none;
            transition: all 0.2s ease;
        }
        .tech-tag:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
        }
        .features {
            list-style-type: none;
            padding-left: 0;
            margin: 0;
        }
        .features li {
            background: linear-gradient(145deg, #ffffff 0%, #f7fafc 100%);
            margin: 15px 0;
            padding: 18px 18px 18px 50px;
            border-radius: 8px;
            border-left: 4px solid #667eea;
            position: relative;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
            transition: all 0.2s ease;
        }
        .features li:hover {
            transform: translateX(4px);
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
        }
        .features li:before {
            content: "⚡";
            position: absolute;
            left: 18px;
            top: 18px;
            color: #667eea;
            font-size: 1.2em;
        }
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        .status-item {
            text-align: center;
            padding: 15px;
            background: linear-gradient(145deg, #ffffff 0%, #f7fafc 100%);
            border-radius: 8px;
            border: 1px solid #e2e8f0;
        }
        .status-value {
            font-size: 1.5em;
            font-weight: 600;
            color: #667eea;
            display: block;
        }
        .status-label {
            font-size: 0.85em;
            color: #718096;
            margin-top: 5px;
        }
        h3 {
            color: #2d3748;
            font-size: 1.4em;
            margin-bottom: 20px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .description-section {
            background: linear-gradient(145deg, #f7fafc 0%, #edf2f7 100%);
            padding: 30px;
            border-radius: 12px;
            border: 1px solid #e2e8f0;
            margin: 30px 0;
        }
        .description-section p {
            margin: 18px 0;
            line-height: 1.8;
        }
        .description-section strong {
            color: #2d3748;
            font-weight: 600;
        }
        .beta-badge {
            display: inline-block;
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: 600;
            margin-left: 10px;
            box-shadow: 0 2px 6px rgba(240, 147, 251, 0.3);
        }
        h3 {
            color: #000080;
            font-size: 1.1em;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Chromepanion Browser Extension</h1>
            <p class="tagline">Your intelligent browsing companion that enhances productivity and provides smart assistance while you browse.</p>
        </div>

        <div class="content">
            <div class="project-info">
                <div class="info-card">
                    <h3>🛠️ Technology Stack</h3>
                    <div class="tech-stack">
                        <span class="tech-tag">TypeScript</span>
                        <span class="tech-tag">Chrome Extension API</span>
                        <span class="tech-tag">React</span>
                        <span class="tech-tag">Webpack</span>
                        <span class="tech-tag">AI/ML APIs</span>
                    </div>
                </div>

                <div class="info-card">
                    <h3>📊 Project Status <span class="beta-badge">BETA</span></h3>
                    <div class="status-grid">
                        <div class="status-item">
                            <span class="status-value">Beta Testing</span>
                            <div class="status-label">Status</div>
                        </div>
                        <div class="status-item">
                            <span class="status-value">2.1.0</span>
                            <div class="status-label">Version</div>
                        </div>
                        <div class="status-item">
                            <span class="status-value">1,200+</span>
                            <div class="status-label">Beta Testers</div>
                        </div>
                        <div class="status-item">
                            <span class="status-value">4.9/5</span>
                            <div class="status-label">Rating</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="info-card">
                <h3>✨ Key Features</h3>
                <ul class="features">
                    <li>AI-powered content summarization and analysis</li>
                    <li>Smart bookmark organization with auto-tagging</li>
                    <li>Real-time language translation overlay</li>
                    <li>Password security analysis and suggestions</li>
                    <li>Tab management with intelligent grouping</li>
                    <li>Privacy protection and tracker blocking</li>
                    <li>Custom shortcuts and automation workflows</li>
                </ul>
            </div>

            <div class="description-section">
                <h3>📝 Description</h3>
                <p>
                    Chromepanion is an advanced browser extension that acts as your personal browsing assistant.
                    Using cutting-edge AI technology, it provides intelligent insights, automates repetitive tasks,
                    and enhances your overall browsing experience.
                </p>
                <p>
                    The extension learns from your browsing patterns to offer personalized suggestions, helps you
                    stay organized with smart bookmark management, and protects your privacy while you explore the web.
                    Perfect for power users who want to maximize their productivity online.
                </p>
            </div>
        </div>
    </div>
</body>
</html>
