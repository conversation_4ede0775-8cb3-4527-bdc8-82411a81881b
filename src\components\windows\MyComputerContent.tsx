
const MyComputerContent = () => {
  const techStack = [
    {
      category: 'Core Technologies',
      items: [
        { name: 'React', version: '18.3.1', description: 'JavaScript library for building dynamic user interfaces' },
        { name: 'TypeScript', version: 'Latest', description: 'Typed superset of JavaScript for robust, scalable applications' },
        { name: 'React Router', version: '6.26.2', description: 'Declarative routing for single-page React applications' }
      ]
    },
    {
      category: 'Styling & UI',
      items: [
        { name: 'Tailwind CSS', version: 'Latest', description: 'A utility-first CSS framework for rapid UI development' },
        { name: 'Shadcn/ui', version: 'Latest', description: 'Beautifully designed, accessible components built on Radix UI' },
        { name: 'Lucide React', version: '0.462.0', description: 'Beautiful & consistent icon toolkit for a clean UI' },
        { name: 'Recharts', version: '2.12.7', description: 'A composable charting library for data visualization' },
        { name: '<PERSON><PERSON>', version: '1.5.0', description: 'An opinionated toast component for user notifications' },
        { name: 'React Resizable Panels', version: '2.1.3', description: 'Components for creating flexible, resizable panel layouts' },
        { name: 'React Icons', version: '5.5.0', description: 'Extensive library of popular icons for React projects' }
      ]
    },
    {
      category: 'State Management & Forms',
      items: [
        { name: 'TanStack Query', version: '5.56.2', description: 'Powerful asynchronous state management and data-fetching' },
        { name: 'React Hook Form', version: '7.53.0', description: 'Performant, flexible forms with easy-to-use validation' },
        { name: 'Zod', version: '3.23.8', description: 'TypeScript-first schema validation with static type inference' }
      ]
    },
    {
      category: 'Build Tools & Utilities',
      items: [
        { name: 'Vite', version: 'Latest', description: 'Next-generation frontend tooling for an optimized dev experience' },
        { name: 'Class Variance Authority', version: '0.7.1', description: 'Create type-safe, composable UI component variants' },
        { name: 'clsx', version: '2.1.1', description: 'A tiny utility for constructing conditional className strings' },
        { name: 'Tailwind Merge', version: '2.5.2', description: 'Merge Tailwind CSS classes without style conflicts' },
        { name: 'tailwindcss-animate', version: '1.0.7', description: 'A Tailwind CSS plugin for orchestrating CSS animations' }
      ]
    }
  ];

  return (
    <div className="p-4 bg-white font-tahoma">
      <div className="flex items-center mb-4 pb-3 border-b-2 border-blue-500">
        <div className="w-8 h-8 bg-blue-500 rounded mr-3 flex items-center justify-center">
          <span className="text-white text-lg">💻</span>
        </div>
        <div>
          <h2 className="text-lg font-bold text-blue-800">System Information</h2>
          <p className="text-sm text-gray-600">Portfolio Application Tech Stack</p>
        </div>
      </div>

      <div className="grid grid-cols-1 gap-4 mb-6">
        <div className="bg-blue-50 p-3 rounded border-l-4 border-blue-500">
          <div className="grid grid-cols-2 gap-2 text-sm">
            <div><strong>System:</strong> Windows XP Portfolio Theme</div>
            <div><strong>Architecture:</strong> Single Page Application</div>
            <div><strong>Runtime:</strong> Modern Web and Mobile Browsers</div>
            <div><strong>Build System:</strong> Vite + TypeScript</div>
          </div>
        </div>
      </div>

      {techStack.map((section, index) => (
        <div key={index} className="mb-6">
          <h3 className="font-bold text-blue-800 mb-3 pb-1 border-b border-gray-300 bg-gradient-to-r from-blue-100 to-transparent px-2 py-1">
            {section.category}
          </h3>
          <div className="space-y-2">
            {section.items.map((item, itemIndex) => (
              <div key={itemIndex} className="bg-gray-50 p-3 rounded border hover:bg-blue-50 transition-colors">
                <div className="flex justify-between items-start mb-1">
                  <span className="font-semibold text-blue-900">{item.name}</span>
                  <span className="text-xs bg-blue-200 text-blue-800 px-2 py-1 rounded">v{item.version}</span>
                </div>
                <p className="text-xs text-gray-600">{item.description}</p>
              </div>
            ))}
          </div>
        </div>
      ))}

      <div className="mt-6 p-4 bg-gradient-to-r from-blue-100 to-purple-100 rounded border">
        <h3 className="font-bold text-gray-800 mb-2 flex items-center">
          Performance Features
        </h3>
        <ul className="text-sm space-y-1 text-gray-700">
          <li>• Hot Module Replacement (HMR) for instant updates via Vite</li>
          <li>• Tree-shaking for optimized bundle size</li>
          <li>• TypeScript for enhanced developer experience and type safety</li>
          <li>• Component-based architecture for reusability and maintainability</li>
          <li>• Utility-first styling with Tailwind for efficient, consistent design</li>
        </ul>
      </div>

      <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded">
        <p className="text-xs text-yellow-800">
          <strong>Note:</strong> My Windows XP themed, mobile-friendly portfolio site showcases modern web technologies 
          wrapped in nostalgic styling. All components are built with accessibility and performance in mind.
        </p>
      </div>
    </div>
  );
};
export default MyComputerContent;
