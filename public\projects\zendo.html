<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Zendo - Browser Extension</title>
    <style>
        body {
            font-family: 'Lucida Grande', 'Trebuchet MS', Verdana, sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #f0f4f8 0%, #e8f0fe 50%, #f8fafc 100%);
            color: #2d3748;
            min-height: 100vh;
            line-height: 1.6;
        }
        .container {
            max-width: 900px;
            margin: 0 auto;
            background: #ffffff;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            margin-top: 20px;
            margin-bottom: 20px;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px 30px;
            text-align: center;
            position: relative;
        }
        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="40" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="40" cy="80" r="1.5" fill="rgba(255,255,255,0.1)"/></svg>');
        }
        h1 {
            font-size: 2.5em;
            margin: 0 0 10px 0;
            font-weight: 300;
            letter-spacing: -1px;
            position: relative;
            z-index: 1;
        }
        .tagline {
            font-size: 1.1em;
            opacity: 0.9;
            font-weight: 300;
            position: relative;
            z-index: 1;
        }
        .content {
            padding: 30px;
        }
        .project-info {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin-bottom: 30px;
        }
        .info-card {
            background: linear-gradient(145deg, #f8fafc 0%, #f1f5f9 100%);
            padding: 25px;
            border-radius: 8px;
            border: 1px solid #e2e8f0;
            flex: 1;
            min-width: 280px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
        }
        .tech-stack {
            margin-top: 15px;
        }
        .tech-tag {
            display: inline-block;
            background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
            color: white;
            padding: 6px 12px;
            margin: 3px;
            font-size: 0.85em;
            border-radius: 15px;
            font-weight: 500;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            border: none;
        }
        .features {
            list-style-type: none;
            padding-left: 0;
            margin: 0;
        }
        .features li {
            background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
            margin: 12px 0;
            padding: 15px 15px 15px 45px;
            border-radius: 6px;
            border: 1px solid #e2e8f0;
            position: relative;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
            transition: all 0.2s ease;
        }
        .features li:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        .features li:before {
            content: "✓";
            position: absolute;
            left: 15px;
            top: 15px;
            color: #48bb78;
            font-weight: bold;
            font-size: 1.1em;
        }
        .screenshot-container {
            text-align: center;
            margin: 30px 0;
            padding: 20px;
            background: linear-gradient(145deg, #f8fafc 0%, #f1f5f9 100%);
            border-radius: 8px;
            border: 1px solid #e2e8f0;
        }
        .screenshot-container img {
            max-width: 100%;
            height: auto;
            border-radius: 6px;
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
        }
        .action-buttons {
            text-align: center;
            margin: 30px 0;
            padding: 20px;
        }
        .chrome-store-btn {
            display: inline-block;
            background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            font-weight: 600;
            border-radius: 6px;
            font-size: 1em;
            box-shadow: 0 4px 12px rgba(66, 153, 225, 0.3);
            transition: all 0.2s ease;
            border: none;
        }
        .chrome-store-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(66, 153, 225, 0.4);
            background: linear-gradient(135deg, #3182ce 0%, #2c5282 100%);
        }
        h3 {
            color: #2d3748;
            font-size: 1.3em;
            margin-bottom: 15px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .description-section {
            background: linear-gradient(145deg, #f8fafc 0%, #f1f5f9 100%);
            padding: 25px;
            border-radius: 8px;
            border: 1px solid #e2e8f0;
            margin: 20px 0;
        }
        .description-section p {
            margin: 15px 0;
            line-height: 1.7;
        }
        .description-section strong {
            color: #2d3748;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Zendo Browser Extension</h1>
            <p class="tagline">Minimalist to-do list with task grouping, drag-and-drop, and color priorities.</p>
        </div>

        <div class="content">
            <!-- Screenshot Section -->
            <div class="screenshot-container">
                <img src="/src/assets/browser-ext/z-task.png" alt="Zendo Extension Screenshot">
            </div>

            <div class="project-info">
                <div class="info-card">
                    <h3>🛠️ Technology Stack</h3>
                    <div class="tech-stack">
                        <span class="tech-tag">JavaScript</span>
                        <span class="tech-tag">Chrome Extension API</span>
                        <span class="tech-tag">HTML5</span>
                        <span class="tech-tag">CSS3</span>
                        <span class="tech-tag">Chrome Sync</span>
                    </div>
                </div>

                <div class="info-card">
                    <h3>📊 Project Status</h3>
                    <p><strong>Status:</strong> Published</p>
                    <p><strong>Version:</strong> Latest</p>
                    <p><strong>Platform:</strong> Chrome Web Store</p>
                    <p><strong>Offline:</strong> Fully supported</p>
                </div>
            </div>

            <!-- Chrome Web Store Link -->
            <div class="action-buttons">
                <a href="https://chromewebstore.google.com/detail/zendo/docecfpjajpjlfpidlbmdodgaopdgdfo"
                   target="_blank"
                   class="chrome-store-btn">
                    View or Install from Chrome Web Store
                </a>
            </div>

            <div class="info-card">
                <h3>✨ Key Features</h3>
                <ul class="features">
                    <li>Grouped Tasks – Organize tasks by project, context, or theme</li>
                    <li>Drag & Drop – Easily reorder tasks and groups with intuitive gestures</li>
                    <li>Custom Priorities – Choose and personalize priority levels using color tags</li>
                    <li>Zen-Inspired Design – Clean, minimal UI with Apple-style visual polish</li>
                    <li>One-Click Complete & Edit – Click to check off tasks or edit inline</li>
                    <li>Chrome Sync – Store tasks and settings across devices</li>
                </ul>
            </div>

            <div class="description-section">
                <h3>📝 Description</h3>
                <p>
                    <strong>Zendo – Grouped To-Do List & Task Manager for Chrome</strong><br>
                    Zendo is a minimalist to-do list extension for Chrome that helps you organize tasks into groups,
                    set color-coded priorities, and stay focused with a clean, drag-and-drop interface.
                </p>
                <p>
                    <strong>Zendo is ideal for:</strong><br>
                    • Daily task planning<br>
                    • Project-based to-do management<br>
                    • Students, creatives, and professionals seeking simplicity<br>
                    • Anyone who prefers grouped over nested tasks
                </p>
                <p>
                    Works fully offline and uses Chrome Sync to store your tasks and settings across devices.<br>
                    <strong>No login. No tracking. No distractions.</strong>
                </p>
            </div>
        </div>
    </div>
</body>
</html>
