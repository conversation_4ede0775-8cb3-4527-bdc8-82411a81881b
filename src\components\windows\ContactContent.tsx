
import { Mail, Phone, Linkedin, Github, Globe, Copy } from 'lucide-react';
import { toast } from 'sonner';
import { Button } from '@/components/ui/button';

const ContactContent = () => {
  const handleCopy = (text: string, label: string) => {
    navigator.clipboard.writeText(text);
    toast.success(`${label} copied to clipboard.`, {
      duration: 2000,
    });
  };

  const contactItems = [
    {
      icon: <Mail size={20} />,
      label: 'Email',
      value: '<EMAIL>',
      href: 'mailto:<EMAIL>',
    },
    {
      icon: <Phone size={20} />,
      label: 'Phone',
      value: '(+63)************',
      href: 'tel:(+63)************',
    },
    {
      icon: <Linkedin size={20} />,
      label: 'LinkedIn',
      value: 'linkedin.com/in/mark-jovet-verano-8637285b',
      href: 'https://www.linkedin.com/in/mark-jovet-verano-8637285b',
    },
    {
      icon: <Github size={20} />,
      label: 'GitHub',
      value: 'github.com/markoverano',
      href: 'https://github.com/markoverano',
    },
    {
      icon: <Globe size={20} />,
      label: 'Portfolio',
      value: 'markoverano.dev',
      href: 'https://markoverano.dev',
    },
  ];

  return (
    <div className="p-4">
      <div className="space-y-4">
        {contactItems.map((item, index) => (
          <div key={index} className="flex items-center space-x-3">
            {item.icon}
            <div className="flex-grow">
              <p className="font-semibold">{item.label}</p>
              <a href={item.href} target="_blank" rel="noopener noreferrer" className="text-sm text-blue-600 cursor-pointer hover:underline">
                {item.value}
              </a>
            </div>
            <Button
              variant="ghost"
              size="icon"
              className="h-8 w-8"
              onClick={() => handleCopy(item.value, item.label)}
              title={`Copy ${item.label}`}
            >
              <Copy size={16} />
              <span className="sr-only">Copy {item.label}</span>
            </Button>
          </div>
        ))}
      </div>
      {/* <div className="mt-6 p-4 bg-gray-50 rounded">
        <h3 className="font-semibold mb-2">Send a Message</h3>
        <form className="space-y-3">
          <input type="text" placeholder="Your Name" className="w-full p-2 border rounded text-sm" />
          <input type="email" placeholder="Your Email" className="w-full p-2 border rounded text-sm" />
          <textarea placeholder="Your Message" rows={3} className="w-full p-2 border rounded text-sm resize-none"></textarea>
          <button type="submit" className="px-4 py-2 bg-blue-600 text-white rounded text-sm hover:bg-blue-700">Send Message</button>
        </form>
      </div> */}
    </div>
  );
};

export default ContactContent;
